"""
高性能商品名称匹配算法

功能概述：
- 预构建匹配器，固定商品列表，仅传入文本进行匹配
- 处理包含关系：当存在"123"和"12345"时，文本"0123456"应匹配为"12345"（偏好更长匹配）
- 允许错别字：以最大编辑距离（最多差几个字符）为阈值的模糊匹配
- 支持商品别名与系列匹配，系列仅用于匹配，返回具体商品

核心接口：
- ProductMatcher(product_catalog) -> matcher
- matcher.match(text, max_edits=1) -> List[商品匹配结果]

返回格式：
[{商品ID（主要识别键），商品名称，匹配信息：{匹配模式，原始文本中的位置，错字信息列表：[错字位置]}}]
"""
from __future__ import annotations

from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Any, Set
import re
from collections import defaultdict

# ------------------------------
# 文本标准化与映射
# ------------------------------

def _to_half_width(s: str) -> str:
    """全角转半角"""
    out = []
    for ch in s:
        code = ord(ch)
        if code == 0x3000:
            code = 32
        elif 0xFF01 <= code <= 0xFF5E:
            code -= 0xFEE0
        out.append(chr(code))
    return "".join(out)

_ALLOWED_RE = re.compile(r"[\u4e00-\u9fff0-9a-z]", re.IGNORECASE)


def normalize_with_map(s: str) -> Tuple[str, List[int]]:
    """标准化字符串，同时返回规范化字符到原始索引的映射列表。

    - 小写、全角转半角
    - 保留中文、字母、数字，去掉其它符号与空白
    """
    if not s:
        return "", []
    s = _to_half_width(s).lower()
    norm_chars: List[str] = []
    idx_map: List[int] = []
    for i, ch in enumerate(s):
        if _ALLOWED_RE.match(ch):
            norm_chars.append(ch)
            idx_map.append(i)
    return "".join(norm_chars), idx_map


def normalize(s: str) -> str:
    return normalize_with_map(s)[0]

# ------------------------------
# 数据结构
# ------------------------------

@dataclass
class ProductInfo:
    """商品信息"""
    product_id: int
    name: str
    patterns: List[str]  # 规范化后的匹配模式列表
    series_key: Optional[str] = None
    series_variant: Optional[str] = None

@dataclass
class MatchCandidate:
    """匹配候选"""
    product_info: ProductInfo
    pattern: str                    # 匹配的具体模式
    start: int                      # 在规范化文本中的开始索引（含）
    end: int                        # 在规范化文本中的结束索引（不含）
    match_type: str                 # 'exact' | 'fuzzy' | 'series_expand'
    edits: int = 0                  # 模糊匹配的编辑距离

    @property
    def length(self) -> int:
        return self.end - self.start

# ------------------------------
# 简单Trie树（用于精确匹配加速）
# ------------------------------

class TrieNode:
    def __init__(self):
        self.children: Dict[str, TrieNode] = {}
        self.products: List[ProductInfo] = []  # 在此节点结束的商品

class Trie:
    def __init__(self):
        self.root = TrieNode()
    
    def insert(self, pattern: str, product: ProductInfo):
        node = self.root
        for char in pattern:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
        node.products.append(product)
    
    def search_all_matches(self, text: str) -> List[MatchCandidate]:
        """在文本中查找所有精确匹配"""
        matches = []
        for start in range(len(text)):
            node = self.root
            for end in range(start, len(text)):
                char = text[end]
                if char not in node.children:
                    break
                node = node.children[char]
                # 检查是否有商品在此位置结束
                for product in node.products:
                    matches.append(MatchCandidate(
                        product_info=product,
                        pattern=text[start:end+1],
                        start=start,
                        end=end+1,
                        match_type='exact'
                    ))
        return matches

# ------------------------------
# Levenshtein（带上界剪枝）
# ------------------------------

def _bounded_levenshtein(a: str, b: str, max_edits: int) -> Optional[int]:
    """计算编辑距离，若距离>max_edits则返回None。"""
    la, lb = len(a), len(b)
    if abs(la - lb) > max_edits:
        return None
    if la > lb:
        a, b = b, a
        la, lb = lb, la
    
    prev = list(range(lb + 1))
    for i in range(1, la + 1):
        start = max(1, i - max_edits)
        end = min(lb, i + max_edits)
        curr = [i] + [10**9] * lb
        min_in_row = 10**9
        for j in range(start, end + 1):
            cost = 0 if a[i - 1] == b[j - 1] else 1
            curr[j] = min(
                prev[j - 1] + cost,
                curr[j - 1] + 1,
                prev[j] + 1,
            )
            if curr[j] < min_in_row:
                min_in_row = curr[j]
        if min_in_row > max_edits:
            return None
        prev = curr
    dist = prev[lb]
    return dist if dist <= max_edits else None

# ------------------------------
# 系列表达式解析
# ------------------------------

def _parse_series_expressions(text: str, series_map: Dict[str, Dict[str, ProductInfo]]) -> List[MatchCandidate]:
    """解析系列表达式，包含泡泡玛特特有的复杂表达式"""
    matches = []

    # 1. 中括号衍生品表达：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】
    bracket_pattern = re.compile(r'([^【]+?)(?:系列)?-?衍生品?【([^】]+)】')
    for match in bracket_pattern.finditer(text):
        series_prefix = match.group(1).strip()
        items_str = match.group(2)

        # 标准化系列前缀
        series_norm = normalize(series_prefix)
        if "系列" not in series_prefix:
            series_norm = normalize(series_prefix + "系列")

        # 分割衍生品项目
        items = re.split(r'[/／、，,]', items_str)

        for item in items:
            item = item.strip()
            if not item:
                continue

            # 尝试匹配具体的子商品
            item_norm = normalize(item)

            # 在系列映射中查找
            if series_norm in series_map:
                for variant_key, product in series_map[series_norm].items():
                    # 检查子商品名称是否包含该项目
                    if item_norm in normalize(product.name) or any(item_norm in normalize(pattern) for pattern in product.patterns):
                        matches.append(MatchCandidate(
                            product_info=product,
                            pattern=f"{series_prefix}衍生品{item}",
                            start=match.start(),
                            end=match.end(),
                            match_type='series_expand'
                        ))

    # 2. 系列-子商品表达：DIMOO心动特调系列-软脸毛绒钥匙扣盲盒
    series_item_pattern = re.compile(r'([^-]+系列)-([^，。；！？\n]+)')
    for match in series_item_pattern.finditer(text):
        series_name = match.group(1).strip()
        item_name = match.group(2).strip()

        series_norm = normalize(series_name)
        item_norm = normalize(item_name)

        # 在系列映射中查找匹配的子商品
        if series_norm in series_map:
            for variant_key, product in series_map[series_norm].items():
                # 检查是否匹配
                if item_norm in normalize(product.name):
                    matches.append(MatchCandidate(
                        product_info=product,
                        pattern=f"{series_name}-{item_name}",
                        start=match.start(),
                        end=match.end(),
                        match_type='series_expand'
                    ))


    return matches

# ------------------------------
# 主匹配器类
# ------------------------------

class ProductMatcher:
    """高性能商品匹配器"""

    def __init__(self, product_catalog: List[Dict[str, Any]]):
        """
        初始化匹配器

        Args:
            product_catalog: 商品目录，格式：
            [
                {
                    "id": 123,  # 整数ID
                    "name": "ABC",
                    "match_names": ["ABC", "A B C"],  # 用于匹配的名称列表
                    "series": {"key": "ABC", "variant": "A"}  # 可选，系列信息
                },
                ...
            ]
        """
        self.products: List[ProductInfo] = []
        self.exact_trie = Trie()
        self.series_map: Dict[str, Dict[str, ProductInfo]] = defaultdict(dict)
        self._build_index(product_catalog)

    def _build_index(self, product_catalog: List[Dict[str, Any]]):
        """构建索引"""
        for item in product_catalog:
            product_id = int(item.get('id', 0))
            name = item.get('name', '')
            match_names = item.get('match_names', [])
            series_info = item.get('series', {})

            if not name or not product_id:
                continue

            # 规范化所有匹配名称
            patterns = []
            all_names = [name] + (match_names or [])
            for n in all_names:
                normalized = normalize(n)
                if normalized and normalized not in patterns:
                    patterns.append(normalized)

            product = ProductInfo(
                product_id=product_id,
                name=name,
                patterns=patterns,
                series_key=series_info.get('key') if series_info else None,
                series_variant=series_info.get('variant') if series_info else None
            )

            self.products.append(product)

            # 构建精确匹配Trie
            for pattern in patterns:
                self.exact_trie.insert(pattern, product)

            # 构建系列映射
            if product.series_key and product.series_variant:
                series_key_norm = normalize(product.series_key)
                variant_norm = normalize(product.series_variant)
                self.series_map[series_key_norm][variant_norm] = product

    def match(self, text: str, max_edits: int = 1) -> List[Dict[str, Any]]:
        """
        匹配文本中的商品

        Args:
            text: 待匹配文本
            max_edits: 最大编辑距离

        Returns:
            List[{
                "商品ID": int,
                "商品名称": str,
                "匹配信息": {
                    "匹配模式": str,
                    "原始文本中的位置": [start, end],
                    "错字信息列表": [错字位置...]
                }
            }]
        """
        text_norm, idx_map = normalize_with_map(text)

        # 1. 精确匹配
        exact_matches = self.exact_trie.search_all_matches(text_norm)

        # 2. 模糊匹配（仅在max_edits > 0时）
        fuzzy_matches = []
        if max_edits > 0:
            fuzzy_matches = self._fuzzy_match(text_norm, max_edits)

        # 3. 系列表达式匹配
        series_matches = _parse_series_expressions(text, self.series_map)

        # 4. 合并所有候选并去重
        all_candidates = exact_matches + fuzzy_matches + series_matches
        selected = self._select_non_overlapping(all_candidates)

        # 5. 构建返回结果
        results = []
        for candidate in selected:
            # 计算原始文本位置
            if candidate.match_type == 'series_expand':
                # 系列表达式使用原始位置
                orig_start, orig_end = candidate.start, candidate.end
            else:
                orig_start, orig_end = self._span_to_original(
                    candidate.start, candidate.end, idx_map
                )

            # 计算错字信息
            typos = []
            if candidate.match_type == 'fuzzy' and candidate.edits > 0:
                seg_norm = text_norm[candidate.start:candidate.end]
                typos = self._calc_typos(candidate.pattern, seg_norm, candidate.start, idx_map)

            result = {
                "商品ID": candidate.product_info.product_id,
                "商品名称": candidate.product_info.name,
                "匹配信息": {
                    "匹配模式": candidate.pattern,
                    "原始文本中的位置": [orig_start, orig_end],
                    "错字信息列表": typos
                }
            }
            results.append(result)

        return results

    def _fuzzy_match(self, text_norm: str, max_edits: int) -> List[MatchCandidate]:
        """模糊匹配"""
        matches = []
        text_len = len(text_norm)

        for product in self.products:
            for pattern in product.patterns:
                pattern_len = len(pattern)
                win_min = max(1, pattern_len - max_edits)
                win_max = pattern_len + max_edits

                for length in range(win_min, min(win_max + 1, text_len + 1)):
                    for start in range(text_len - length + 1):
                        segment = text_norm[start:start + length]
                        dist = _bounded_levenshtein(pattern, segment, max_edits)
                        if dist is not None:
                            matches.append(MatchCandidate(
                                product_info=product,
                                pattern=pattern,
                                start=start,
                                end=start + length,
                                match_type='fuzzy',
                                edits=dist
                            ))

        return matches

    def _select_non_overlapping(self, candidates: List[MatchCandidate]) -> List[MatchCandidate]:
        """选择不重叠的候选，优先级：长度 > 精确匹配 > 编辑距离小 > 位置靠前"""
        def priority_key(c: MatchCandidate):
            return (
                -c.length,  # 长度优先（降序）
                0 if c.match_type == 'exact' else 1,  # 精确匹配优先
                c.edits,  # 编辑距离小优先
                c.start,  # 位置靠前优先
            )

        candidates_sorted = sorted(candidates, key=priority_key)
        selected = []
        occupied_spans = []

        for candidate in candidates_sorted:
            span = (candidate.start, candidate.end)
            # 检查是否与已选择的候选重叠
            overlapped = any(
                not (span[1] <= occ[0] or occ[1] <= span[0])
                for occ in occupied_spans
            )
            if not overlapped:
                selected.append(candidate)
                occupied_spans.append(span)

        return selected

    def _span_to_original(self, start: int, end: int, idx_map: List[int]) -> Tuple[int, int]:
        """将规范化文本位置转换为原始文本位置"""
        if not idx_map or start >= len(idx_map):
            return (0, 0)

        orig_start = idx_map[start]
        orig_end = (idx_map[end - 1] + 1) if end > start and (end - 1) < len(idx_map) else orig_start
        return (orig_start, orig_end)

    def _calc_typos(self, pattern: str, segment: str, seg_start: int, idx_map: List[int]) -> List[int]:
        """计算错字位置（简化版，返回原始文本中的错字位置）"""
        typos = []
        lp, ls = len(pattern), len(segment)

        # 构建DP表
        dp = [[0] * (ls + 1) for _ in range(lp + 1)]
        for i in range(lp + 1):
            dp[i][0] = i
        for j in range(ls + 1):
            dp[0][j] = j

        for i in range(1, lp + 1):
            for j in range(1, ls + 1):
                cost = 0 if pattern[i-1] == segment[j-1] else 1
                dp[i][j] = min(
                    dp[i-1][j] + 1,      # 删除
                    dp[i][j-1] + 1,      # 插入
                    dp[i-1][j-1] + cost  # 替换
                )

        # 简单回溯找错字位置
        i, j = lp, ls
        while i > 0 and j > 0:
            if pattern[i-1] != segment[j-1] and dp[i][j] == dp[i-1][j-1] + 1:
                # 替换操作，记录原始文本位置
                orig_pos = idx_map[seg_start + j - 1] if (seg_start + j - 1) < len(idx_map) else None
                if orig_pos is not None:
                    typos.append(orig_pos)
            i -= 1
            j -= 1

        return sorted(typos)


# ------------------------------
# 全局匹配器实例（单例模式）
# ------------------------------

_global_matcher: Optional[ProductMatcher] = None

def build_matcher(product_catalog: List[Dict[str, Any]]) -> ProductMatcher:
    """构建匹配器"""
    return ProductMatcher(product_catalog)

def set_global_matcher(product_catalog: List[Dict[str, Any]]):
    """设置全局匹配器"""
    global _global_matcher
    _global_matcher = ProductMatcher(product_catalog)

def get_global_matcher() -> Optional[ProductMatcher]:
    """获取全局匹配器"""
    return _global_matcher

def match_products(text: str, max_edits: int = 1) -> List[Dict[str, Any]]:
    """
    使用全局匹配器进行匹配

    Args:
        text: 待匹配文本
        max_edits: 最大编辑距离

    Returns:
        匹配结果列表
    """
    matcher = get_global_matcher()
    if matcher is None:
        return []
    return matcher.match(text, max_edits)


__all__ = [
    'ProductMatcher',
    'build_matcher',
    'set_global_matcher',
    'get_global_matcher',
    'match_products',
    'normalize',
]
